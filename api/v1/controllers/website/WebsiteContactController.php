<?php
/**
 * Contrôleur API pour les demandes de contact (vitrine)
 * 
 * @package TechCMS
 * @version 1.0.0
 */

namespace TechCMS\Api\V1\Controllers\website;

use TechCMS\Api\V1\Controllers\ApiBaseController;
use TechCMS\Common\Core\Logger;
use TechCMS\Common\Core\Database;

class WebsiteContactController extends ApiBaseController {
    protected $db;

    public function __construct() {
        parent::__construct();
        $this->db = Database::getInstance()->getConnection();
    }

    /**
     * Traite une demande de contact depuis la vitrine
     */
    public function send() {
        try {
            $data = $this->getJsonInput();
            
            // Validation des champs requis
            $required = ['name', 'email', 'subject', 'message'];
            $errors = [];
            
            foreach ($required as $field) {
                if (!isset($data[$field]) || empty(trim($data[$field]))) {
                    $errors[$field] = "Le champ {$field} est requis";
                }
            }

            // Validation de l'email
            if (isset($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = 'Format d\'email invalide';
            }

            // Validation de la longueur
            if (isset($data['message']) && strlen($data['message']) < 10) {
                $errors['message'] = 'Le message doit contenir au moins 10 caractères';
            }

            if (!empty($errors)) {
                $this->sendError('Données invalides', 400, $errors);
                return;
            }

            // Protection anti-spam basique
            $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            if ($this->isSpamming($ip)) {
                $this->sendError('Trop de demandes. Veuillez patienter avant de renvoyer un message.', 429);
                return;
            }

            // Enregistrer la demande de contact
            $contactId = $this->saveContactRequest($data, $ip);
            
            if ($contactId) {
                // Envoyer une notification par email (optionnel)
                $this->sendNotificationEmail($data);
                
                Logger::channel('api')->info('Demande de contact enregistrée', [
                    'contact_id' => $contactId,
                    'email' => $data['email'],
                    'subject' => $data['subject'],
                    'ip' => $ip
                ]);

                $this->sendResponse([
                    'message' => 'Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.'
                ]);
            } else {
                $this->sendError('Erreur lors de l\'envoi du message', 500);
            }

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors du traitement de la demande de contact', [
                'error' => $e->getMessage(),
                'data' => $data ?? null,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            $this->sendError('Erreur lors de l\'envoi du message', 500);
        }
    }

    /**
     * Vérifie si l'IP fait du spam
     */
    private function isSpamming($ip) {
        try {
            // Vérifier le nombre de demandes dans les 10 dernières minutes
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count 
                FROM contact_requests 
                WHERE ip_address = :ip 
                AND created_at > DATE_SUB(NOW(), INTERVAL 10 MINUTE)
            ");
            $stmt->execute(['ip' => $ip]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            return $result['count'] >= 3; // Maximum 3 demandes par 10 minutes
        } catch (\Exception $e) {
            Logger::channel('api')->warning('Erreur lors de la vérification anti-spam', [
                'error' => $e->getMessage(),
                'ip' => $ip
            ]);
            return false; // En cas d'erreur, ne pas bloquer
        }
    }

    /**
     * Enregistre la demande de contact en base
     */
    private function saveContactRequest($data, $ip) {
        try {
            // Créer la table si elle n'existe pas
            $this->createContactTableIfNotExists();
            
            $stmt = $this->db->prepare("
                INSERT INTO contact_requests (
                    name, email, company, subject, message, ip_address, created_at
                ) VALUES (
                    :name, :email, :company, :subject, :message, :ip, NOW()
                )
            ");
            
            $result = $stmt->execute([
                'name' => trim($data['name']),
                'email' => trim($data['email']),
                'company' => isset($data['company']) ? trim($data['company']) : null,
                'subject' => trim($data['subject']),
                'message' => trim($data['message']),
                'ip' => $ip
            ]);
            
            return $result ? $this->db->lastInsertId() : false;
        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de l\'enregistrement de la demande de contact', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            return false;
        }
    }

    /**
     * Crée la table contact_requests si elle n'existe pas
     */
    private function createContactTableIfNotExists() {
        try {
            $this->db->exec("
                CREATE TABLE IF NOT EXISTS contact_requests (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    email VARCHAR(255) NOT NULL,
                    company VARCHAR(255) NULL,
                    subject VARCHAR(500) NOT NULL,
                    message TEXT NOT NULL,
                    ip_address VARCHAR(45) NULL,
                    status ENUM('new', 'read', 'replied') DEFAULT 'new',
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NULL,
                    INDEX idx_created_at (created_at),
                    INDEX idx_status (status),
                    INDEX idx_ip_address (ip_address)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la création de la table contact_requests', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Envoie une notification par email (optionnel)
     */
    private function sendNotificationEmail($data) {
        try {
            // TODO: Implémenter l'envoi d'email si nécessaire
            // Utiliser PHPMailer ou le système de mail existant
            
            Logger::channel('api')->info('Notification email de contact (non implémentée)', [
                'from' => $data['email'],
                'subject' => $data['subject']
            ]);
        } catch (\Exception $e) {
            Logger::channel('api')->warning('Erreur lors de l\'envoi de la notification email', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
