<?php

namespace TechCMS\Api\V1\Controllers\website;

use TechCMS\Api\V1\Controllers\ApiBaseController;
use TechCMS\Common\Core\Logger;

/**
 * Contrôleur de logs pour le website/boutique
 */
class WebsiteLogController extends ApiBaseController
{
    /**
     * Enregistrer un log frontend
     */
    public function store()
    {
        try {
            $data = $this->getRequestData();
            
            // Validation des données
            if (empty($data['level']) || empty($data['message'])) {
                $this->sendError('Level et message requis', 400);
                return;
            }
            
            $level = strtolower($data['level']);
            $message = $data['message'];
            $context = $data['context'] ?? [];
            
            // Ajouter des informations de contexte
            $context['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            $context['ip'] = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $context['url'] = $data['url'] ?? '';
            $context['timestamp'] = date('Y-m-d H:i:s');
            
            // Enregistrer le log selon le niveau
            switch ($level) {
                case 'error':
                    Logger::channel('website-frontend')->error($message, $context);
                    break;
                case 'warning':
                case 'warn':
                    Logger::channel('website-frontend')->warning($message, $context);
                    break;
                case 'info':
                    Logger::channel('website-frontend')->info($message, $context);
                    break;
                case 'debug':
                    Logger::channel('website-frontend')->debug($message, $context);
                    break;
                default:
                    Logger::channel('website-frontend')->info($message, $context);
            }
            
            $this->sendResponse([
                'message' => 'Log enregistré avec succès'
            ], 200);
            
        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de l\'enregistrement du log frontend', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError('Erreur lors de l\'enregistrement du log', 500);
        }
    }
    
    /**
     * Enregistrer plusieurs logs en batch
     */
    public function batch()
    {
        try {
            $data = $this->getRequestData();
            
            if (empty($data['logs']) || !is_array($data['logs'])) {
                $this->sendError('Tableau de logs requis', 400);
                return;
            }
            
            $processed = 0;
            $errors = [];
            
            foreach ($data['logs'] as $index => $logData) {
                try {
                    if (empty($logData['level']) || empty($logData['message'])) {
                        $errors[] = "Log {$index}: Level et message requis";
                        continue;
                    }
                    
                    $level = strtolower($logData['level']);
                    $message = $logData['message'];
                    $context = $logData['context'] ?? [];
                    
                    // Ajouter des informations de contexte
                    $context['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
                    $context['ip'] = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
                    $context['url'] = $logData['url'] ?? '';
                    $context['timestamp'] = $logData['timestamp'] ?? date('Y-m-d H:i:s');
                    $context['batch_index'] = $index;
                    
                    // Enregistrer le log selon le niveau
                    switch ($level) {
                        case 'error':
                            Logger::channel('website-frontend')->error($message, $context);
                            break;
                        case 'warning':
                        case 'warn':
                            Logger::channel('website-frontend')->warning($message, $context);
                            break;
                        case 'info':
                            Logger::channel('website-frontend')->info($message, $context);
                            break;
                        case 'debug':
                            Logger::channel('website-frontend')->debug($message, $context);
                            break;
                        default:
                            Logger::channel('website-frontend')->info($message, $context);
                    }
                    
                    $processed++;
                    
                } catch (\Exception $e) {
                    $errors[] = "Log {$index}: " . $e->getMessage();
                }
            }
            
            $this->sendResponse([
                'message' => "Batch traité: {$processed} logs enregistrés",
                'processed' => $processed,
                'errors' => $errors
            ], 200);
            
        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors du traitement du batch de logs', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError('Erreur lors du traitement du batch', 500);
        }
    }
    
    /**
     * Récupère les données de la requête (JSON, POST ou GET)
     */
    private function getRequestData() {
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);
        
        // Fallback vers $_POST si pas de JSON
        if (!$data) {
            $data = $_POST;
        }
        
        // Fallback vers $_GET pour certaines requêtes
        if (!$data) {
            $data = $_GET;
        }
        
        return $data ?: [];
    }
}
