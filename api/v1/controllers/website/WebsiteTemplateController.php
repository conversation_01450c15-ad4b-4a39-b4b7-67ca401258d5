<?php
/**
 * Contrôleur API publique pour les templates de licences (vitrine)
 * 
 * @package TechCMS
 * @version 1.0.0
 */

namespace TechCMS\Api\V1\Controllers\Website;

use TechCMS\Api\V1\Controllers\ApiBaseController;
use TechCMS\Common\Models\LicenseTemplateModel;
use TechCMS\Common\Core\Logger;

class TemplateController extends ApiBaseController {
    protected $templateModel;

    public function __construct() {
        parent::__construct();
        $this->templateModel = new LicenseTemplateModel();
    }

    /**
     * Récupère tous les templates actifs pour la vitrine
     */
    public function index() {
        try {
            $templates = $this->templateModel->getActiveTemplates();
            
            Logger::channel('api')->info('Templates vitrine récupérés', [
                'count' => count($templates),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            $this->sendResponse([
                'templates' => $templates
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération des templates vitrine', [
                'error' => $e->getMessage(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            $this->sendError('Erreur lors de la récupération des templates', 500);
        }
    }

    /**
     * Récupère les templates mis en avant pour la page d'accueil
     */
    public function featured() {
        try {
            $limit = (int)($_GET['limit'] ?? 3);
            $templates = $this->templateModel->getFeaturedTemplates($limit);
            
            Logger::channel('api')->info('Templates mis en avant récupérés', [
                'count' => count($templates),
                'limit' => $limit,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            $this->sendResponse([
                'templates' => $templates
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération des templates mis en avant', [
                'error' => $e->getMessage(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            $this->sendError('Erreur lors de la récupération des templates', 500);
        }
    }

    /**
     * Récupère un template spécifique par son ID (pour la page de détail)
     */
    public function show($id) {
        try {
            $template = $this->templateModel->getTemplateById($id);
            
            if (!$template || $template['status'] !== 'active') {
                $this->sendError('Template non trouvé ou inactif', 404);
                return;
            }

            Logger::channel('api')->info('Template vitrine récupéré', [
                'template_id' => $id,
                'template_name' => $template['name'],
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            $this->sendResponse([
                'template' => $template
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération du template vitrine', [
                'id' => $id,
                'error' => $e->getMessage(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            $this->sendError('Erreur lors de la récupération du template', 500);
        }
    }

    /**
     * Récupère les statistiques publiques (nombre de templates actifs)
     */
    public function stats() {
        try {
            $stats = $this->templateModel->getTemplateStats();
            
            // Ne retourner que les statistiques publiques
            $publicStats = [
                'active_templates' => $stats['active'],
                'featured_templates' => $stats['featured']
            ];

            Logger::channel('api')->info('Statistiques vitrine récupérées', [
                'stats' => $publicStats,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);

            $this->sendResponse([
                'stats' => $publicStats
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération des statistiques vitrine', [
                'error' => $e->getMessage(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            $this->sendError('Erreur lors de la récupération des statistiques', 500);
        }
    }
}
