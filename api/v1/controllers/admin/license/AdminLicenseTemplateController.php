<?php
/**
 * Contrôleur API pour la gestion des templates de licences
 * 
 * @package TechCMS
 * @version 1.0.0
 */

namespace TechCMS\Api\V1\Controllers\Admin;

use TechCMS\Api\V1\Controllers\ApiBaseController;
use TechCMS\Common\Models\LicenseTemplateModel;
use TechCMS\Common\Core\Logger;

class LicenseTemplateController extends ApiBaseController {
    protected $templateModel;

    public function __construct() {
        parent::__construct();
        $this->templateModel = new LicenseTemplateModel();
    }

    /**
     * Liste tous les templates avec pagination
     */
    public function index() {
        try {
            $page = max(1, (int)($_GET['page'] ?? 1));
            $perPage = max(1, min(100, (int)($_GET['per_page'] ?? 15))); // Limite entre 1 et 100
            $status = $_GET['status'] ?? null;

            // Utiliser la méthode du modèle
            $result = $this->templateModel->getTemplatesWithPagination($page, $perPage, $status);

            $this->sendResponse($result);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération des templates', [
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la récupération des templates', 500);
        }
    }

    /**
     * Récupère un template spécifique
     */
    public function show($id) {
        try {
            $template = $this->templateModel->getTemplateById($id);
            
            if (!$template) {
                $this->sendError('Template non trouvé', 404);
                return;
            }

            $this->sendResponse(['template' => $template]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération du template', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la récupération du template', 500);
        }
    }

    /**
     * Crée un nouveau template
     */
    public function store() {
        try {
            $data = $this->getRequestData();
            
            // Validation des champs requis
            $required = ['name', 'domain_limit', 'installation_limit'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || empty($data[$field])) {
                    $this->sendError("Le champ {$field} est requis", 400);
                    return;
                }
            }

            // Validation spéciale pour le prix (0 autorisé)
            if (!isset($data['price']) || !is_numeric($data['price'])) {
                $this->sendError("Le champ price est requis et doit être numérique", 400);
                return;
            }

            // Validation du prix (0€ autorisé pour les templates gratuits)
            if ($data['price'] < 0) {
                $this->sendError('Le prix doit être positif ou nul (0€ pour gratuit)', 400);
                return;
            }

            // Validation des limites
            if (!is_numeric($data['domain_limit']) || $data['domain_limit'] < 1) {
                $this->sendError('La limite de domaines doit être un nombre positif', 400);
                return;
            }

            if (!is_numeric($data['installation_limit']) || $data['installation_limit'] < 1) {
                $this->sendError('La limite d\'installations doit être un nombre positif', 400);
                return;
            }

            $templateId = $this->templateModel->createTemplate($data);

            if ($templateId) {
                $template = $this->templateModel->getTemplateById($templateId);
                
                $this->sendResponse([
                    'message' => 'Template créé avec succès',
                    'template' => $template
                ], 201);
            } else {
                $this->sendError('Erreur lors de la création du template', 500);
            }

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la création du template', [
                'data' => $data ?? null,
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la création du template', 500);
        }
    }

    /**
     * Met à jour un template existant
     */
    public function update($id) {
        try {
            $template = $this->templateModel->getTemplateById($id);
            if (!$template) {
                $this->sendError('Template non trouvé', 404);
                return;
            }

            $data = $this->getRequestData();
            
            // Validation du prix si fourni
            if (isset($data['price']) && (!is_numeric($data['price']) || $data['price'] < 0)) {
                $this->sendError('Le prix doit être un nombre positif', 400);
                return;
            }

            // Validation des limites si fournies
            if (isset($data['domain_limit']) && (!is_numeric($data['domain_limit']) || $data['domain_limit'] < 1)) {
                $this->sendError('La limite de domaines doit être un nombre positif', 400);
                return;
            }

            if (isset($data['installation_limit']) && (!is_numeric($data['installation_limit']) || $data['installation_limit'] < 1)) {
                $this->sendError('La limite d\'installations doit être un nombre positif', 400);
                return;
            }

            $result = $this->templateModel->updateTemplate($id, $data);
            
            if ($result) {
                $updatedTemplate = $this->templateModel->getTemplateById($id);
                $this->sendResponse([
                    'message' => 'Template mis à jour avec succès',
                    'template' => $updatedTemplate
                ]);
            } else {
                $this->sendError('Erreur lors de la mise à jour du template', 500);
            }

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la mise à jour du template', [
                'id' => $id,
                'data' => $data ?? null,
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la mise à jour du template', 500);
        }
    }

    /**
     * Supprime un template (soft delete)
     */
    public function destroy($id) {
        try {
            $template = $this->templateModel->getTemplateById($id);
            if (!$template) {
                $this->sendError('Template non trouvé', 404);
                return;
            }

            $result = $this->templateModel->deleteTemplate($id);
            
            if ($result) {
                $this->sendResponse(['message' => 'Template supprimé avec succès']);
            } else {
                $this->sendError('Erreur lors de la suppression du template', 500);
            }

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la suppression du template', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la suppression du template', 500);
        }
    }

    /**
     * Récupère les statistiques des templates
     */
    public function stats() {
        try {
            $stats = $this->templateModel->getTemplateStats();
            $this->sendResponse(['stats' => $stats]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération des statistiques', [
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la récupération des statistiques', 500);
        }
    }

    /**
     * Met à jour l'ordre d'affichage des templates
     */
    public function updateOrder() {
        try {
            $data = $this->getRequestData();
            
            if (!isset($data['template_ids']) || !is_array($data['template_ids'])) {
                $this->sendError('Liste des IDs de templates requise', 400);
                return;
            }

            $result = $this->templateModel->updateSortOrder($data['template_ids']);
            
            if ($result) {
                $this->sendResponse(['message' => 'Ordre mis à jour avec succès']);
            } else {
                $this->sendError('Erreur lors de la mise à jour de l\'ordre', 500);
            }

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la mise à jour de l\'ordre', [
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la mise à jour de l\'ordre', 500);
        }
    }

    /**
     * Récupère les données de la requête (JSON, POST ou GET)
     */
    private function getRequestData() {
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        // Fallback vers $_POST si pas de JSON
        if (!$data) {
            $data = $_POST;
        }

        // Fallback vers $_GET pour certaines requêtes
        if (!$data) {
            $data = $_GET;
        }

        return $data ?: [];
    }
}
