<?php
/**
 * Contrôleur API pour les mises à jour CMS côté client
 * 
 * @package TechCMS
 * @version 1.0.0
 */

namespace TechCMS\Api\V1\Controllers\Client;

use TechCMS\Api\V1\Controllers\Client\ClientBaseController;
use TechCMS\Common\Models\CmsVersionModel;
use TechCMS\Common\Models\UpdateDownloadModel;
use TechCMS\Common\Models\LicenseModel;
use TechCMS\Common\Core\Logger;

class UpdatesController extends ClientBaseController {
    protected $versionModel;
    protected $downloadModel;
    protected $licenseModel;

    public function __construct() {
        parent::__construct();
        $this->versionModel = new CmsVersionModel();
        $this->downloadModel = new UpdateDownloadModel();
        $this->licenseModel = new LicenseModel();
    }

    /**
     * Récupère les versions disponibles selon le canal de la licence
     */
    public function getVersions() {
        try {
            $clientId = $this->getClientId();
            $license = $this->getClientLicense($clientId);
            
            if (!$license) {
                $this->sendError('Licence non trouvée', 404);
                return;
            }

            $channel = $_GET['channel'] ?? $license['channel'] ?? 'Stable';
            $versions = $this->versionModel->getVersionsByChannel($channel);
            
            Logger::channel('api')->info('Versions récupérées pour client', [
                'client_id' => $clientId,
                'license_id' => $license['id'],
                'channel' => $channel,
                'versions_count' => count($versions)
            ]);

            $this->sendResponse([
                'versions' => $versions
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération des versions', [
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la récupération des versions', 500);
        }
    }

    /**
     * Récupère les informations de licence pour les mises à jour
     */
    public function getLicenseInfo() {
        try {
            $clientId = $this->getClientId();
            $license = $this->getClientLicense($clientId);
            
            if (!$license) {
                $this->sendError('Licence non trouvée', 404);
                return;
            }

            // Compter les mises à jour disponibles
            $availableUpdates = $this->versionModel->countAvailableUpdates(
                $license['channel'] ?? 'Stable',
                $license['current_version'] ?? null
            );

            $licenseInfo = [
                'license_id' => $license['id'],
                'license_key' => $license['license_key'],
                'domain' => $license['allowed_domains'] ?? null,
                'current_version' => $license['current_version'] ?? null,
                'update_permissions' => (bool)$license['update_permissions'],
                'channel' => $license['channel'] ?? 'Stable',
                'last_update_check' => $license['last_update_check'] ?? null,
                'available_updates' => $availableUpdates
            ];

            $this->sendResponse([
                'license_info' => $licenseInfo
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération des infos de licence', [
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la récupération des informations', 500);
        }
    }

    /**
     * Récupère les statistiques de téléchargement
     */
    public function getStats() {
        try {
            $clientId = $this->getClientId();
            $license = $this->getClientLicense($clientId);
            
            if (!$license) {
                $this->sendError('Licence non trouvée', 404);
                return;
            }

            $stats = $this->downloadModel->getDownloadStats($license['id']);
            
            $this->sendResponse([
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération des statistiques', [
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la récupération des statistiques', 500);
        }
    }

    /**
     * Génère un token de téléchargement pour une version
     */
    public function generateDownloadToken() {
        try {
            $clientId = $this->getClientId();
            $license = $this->getClientLicense($clientId);
            
            if (!$license) {
                $this->sendError('Licence non trouvée', 404);
                return;
            }

            // Vérifier les permissions de mise à jour
            if (!$license['update_permissions']) {
                $this->sendError('Mises à jour non autorisées pour cette licence', 403);
                return;
            }

            $data = $this->getRequestData();
            $versionId = $data['version_id'] ?? null;

            if (!$versionId) {
                $this->sendError('ID de version requis', 400);
                return;
            }

            // Vérifier que la version existe et est accessible
            $version = $this->versionModel->find($versionId);
            if (!$version) {
                $this->sendError('Version non trouvée', 404);
                return;
            }

            // Toutes les versions sont accessibles (pas de restriction par canal)

            // Générer le token de téléchargement
            $downloadToken = $this->downloadModel->generateDownloadToken(
                $license['id'],
                $versionId,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            );

            if ($downloadToken) {
                $downloadUrl = $this->generateDownloadUrl($downloadToken);

                Logger::channel('api')->info('Token de téléchargement généré', [
                    'client_id' => $clientId,
                    'license_id' => $license['id'],
                    'version_id' => $versionId,
                    'token' => substr($downloadToken, 0, 8) . '...'
                ]);

                $this->sendResponse([
                    'download' => [
                        'token' => $downloadToken,
                        'url' => $downloadUrl,
                        'expires_at' => date('Y-m-d H:i:s', strtotime('+24 hours')),
                        'filename' => 'techcms-' . $version['version'] . '.zip'
                    ]
                ]);
            } else {
                $this->sendError('Erreur lors de la génération du token', 500);
            }

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la génération du token', [
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la génération du token', 500);
        }
    }

    /**
     * Vérifie les mises à jour disponibles
     */
    public function checkUpdates() {
        try {
            $clientId = $this->getClientId();
            $license = $this->getClientLicense($clientId);
            
            if (!$license) {
                $this->sendError('Licence non trouvée', 404);
                return;
            }

            // Mettre à jour la date de dernière vérification
            $this->licenseModel->update($license['id'], [
                'last_update_check' => date('Y-m-d H:i:s')
            ]);

            // Compter les nouvelles mises à jour
            $availableUpdates = $this->versionModel->countAvailableUpdates(
                $license['channel'] ?? 'Stable',
                $license['current_version'] ?? null
            );

            Logger::channel('api')->info('Vérification des mises à jour', [
                'client_id' => $clientId,
                'license_id' => $license['id'],
                'available_updates' => $availableUpdates
            ]);

            $this->sendResponse([
                'available_updates' => $availableUpdates,
                'message' => $availableUpdates > 0 
                    ? "{$availableUpdates} mise(s) à jour disponible(s)"
                    : "Aucune mise à jour disponible"
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la vérification des mises à jour', [
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la vérification', 500);
        }
    }

    /**
     * Méthodes utilitaires
     */
    private function getClientLicense($clientId) {
        // Récupérer les licences du client
        $licenses = $this->licenseModel->getClientLicenses($clientId);

        if (empty($licenses)) {
            return null;
        }

        // Filtrer les licences actives avec droits de mise à jour
        $validLicenses = array_filter($licenses, function($license) {
            return $license['status'] === 'active' && $license['update_permissions'];
        });

        if (empty($validLicenses)) {
            return null;
        }

        // Sélection intelligente : critères de priorité
        // 1. Licence avec version la plus récente
        // 2. Licence créée le plus récemment
        // 3. Licence avec domaine défini (plus complète)

        usort($validLicenses, function($a, $b) {
            // Priorité 1 : Version la plus récente
            if (!empty($a['current_version']) && !empty($b['current_version'])) {
                $versionCompare = version_compare($a['current_version'], $b['current_version']);
                if ($versionCompare !== 0) {
                    return -$versionCompare; // Plus récente en premier
                }
            } elseif (!empty($a['current_version'])) {
                return -1; // A a une version, B non
            } elseif (!empty($b['current_version'])) {
                return 1; // B a une version, A non
            }

            // Priorité 2 : Date de création plus récente
            $dateA = strtotime($a['created_at']);
            $dateB = strtotime($b['created_at']);
            if ($dateA !== $dateB) {
                return $dateB - $dateA; // Plus récente en premier
            }

            // Priorité 3 : Licence avec domaine défini
            if (!empty($a['domain']) && empty($b['domain'])) {
                return -1;
            } elseif (empty($a['domain']) && !empty($b['domain'])) {
                return 1;
            }

            // Priorité 4 : ID plus élevé (plus récent)
            return $b['id'] - $a['id'];
        });

        // Retourner la meilleure licence selon les critères
        return $validLicenses[0];
    }

    private function generateDownloadUrl($token) {
        // Générer l'URL de téléchargement sécurisée
        $baseUrl = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';

        return "{$protocol}://{$baseUrl}/api/v1/updates/download/{$token}";
    }

    /**
     * Récupère les données de la requête (JSON, POST ou GET)
     */
    private function getRequestData() {
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        // Fallback vers $_POST si pas de JSON
        if (!$data) {
            $data = $_POST;
        }

        // Fallback vers $_GET pour certaines requêtes
        if (!$data) {
            $data = $_GET;
        }

        return $data ?: [];
    }
}
