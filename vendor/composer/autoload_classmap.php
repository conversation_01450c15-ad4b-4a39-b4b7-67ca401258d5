<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Attribute' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
    'CURLStringFile' => $vendorDir . '/symfony/polyfill-php81/Resources/stubs/CURLStringFile.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Normalizer' => $vendorDir . '/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php',
    'PhpToken' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
    'ReturnTypeWillChange' => $vendorDir . '/symfony/polyfill-php81/Resources/stubs/ReturnTypeWillChange.php',
    '<PERSON><PERSON><PERSON><PERSON>n\\Diff\\Chunk' => $vendorDir . '/sebastian/diff/src/Chunk.php',
    'SebastianBergmann\\Diff\\ConfigurationException' => $vendorDir . '/sebastian/diff/src/Exception/ConfigurationException.php',
    'SebastianBergmann\\Diff\\Diff' => $vendorDir . '/sebastian/diff/src/Diff.php',
    'SebastianBergmann\\Diff\\Differ' => $vendorDir . '/sebastian/diff/src/Differ.php',
    'SebastianBergmann\\Diff\\Exception' => $vendorDir . '/sebastian/diff/src/Exception/Exception.php',
    'SebastianBergmann\\Diff\\InvalidArgumentException' => $vendorDir . '/sebastian/diff/src/Exception/InvalidArgumentException.php',
    'SebastianBergmann\\Diff\\Line' => $vendorDir . '/sebastian/diff/src/Line.php',
    'SebastianBergmann\\Diff\\LongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/LongestCommonSubsequenceCalculator.php',
    'SebastianBergmann\\Diff\\MemoryEfficientLongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/MemoryEfficientLongestCommonSubsequenceCalculator.php',
    'SebastianBergmann\\Diff\\Output\\AbstractChunkOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/AbstractChunkOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\DiffOnlyOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/DiffOnlyOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\DiffOutputBuilderInterface' => $vendorDir . '/sebastian/diff/src/Output/DiffOutputBuilderInterface.php',
    'SebastianBergmann\\Diff\\Output\\StrictUnifiedDiffOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/StrictUnifiedDiffOutputBuilder.php',
    'SebastianBergmann\\Diff\\Output\\UnifiedDiffOutputBuilder' => $vendorDir . '/sebastian/diff/src/Output/UnifiedDiffOutputBuilder.php',
    'SebastianBergmann\\Diff\\Parser' => $vendorDir . '/sebastian/diff/src/Parser.php',
    'SebastianBergmann\\Diff\\TimeEfficientLongestCommonSubsequenceCalculator' => $vendorDir . '/sebastian/diff/src/TimeEfficientLongestCommonSubsequenceCalculator.php',
    'Stringable' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
    'TechCMS\\Admin\\Models\\AdminModel' => $baseDir . '/includes/admin/models/AdminModel.php',
    'TechCMS\\Admin\\Models\\AdminProductDraft' => $baseDir . '/includes/admin/models/AdminProductDraft.php',
    'TechCMS\\Admin\\Models\\AdminServerModel' => $baseDir . '/includes/admin/models/AdminServerModel.php',
    'TechCMS\\Admin\\Models\\AdminServiceConfigurationModel' => $baseDir . '/includes/admin/models/AdminServiceConfigurationModel.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\AdminAuthController' => $baseDir . '/api/v1/controllers/admin/AdminAuthController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\AdminBaseController' => $baseDir . '/api/v1/controllers/admin/AdminBaseController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\Client\\AdminClientController' => $baseDir . '/api/v1/controllers/admin/client/AdminClientController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\Invoice\\AdminInvoiceController' => $baseDir . '/api/v1/controllers/admin/invoice/AdminInvoiceController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\LicenseTemplateController' => $baseDir . '/api/v1/controllers/Admin/LicenseTemplateController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\License\\AdminLicenseController' => $baseDir . '/api/v1/controllers/admin/license/AdminLicenseController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\License\\AdminLicenseUpdateController' => $baseDir . '/api/v1/controllers/admin/license/AdminLicenseUpdateController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\Module\\AdminModuleController' => $baseDir . '/api/v1/controllers/admin/module/AdminModuleController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\Payment\\AdminPaymentController' => $baseDir . '/api/v1/controllers/admin/payment/AdminPaymentController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminAutomationController' => $baseDir . '/api/v1/controllers/admin/system/AdminAutomationController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminDashboardController' => $baseDir . '/api/v1/controllers/admin/system/AdminDashboardController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminLogController' => $baseDir . '/api/v1/controllers/admin/system/AdminLogController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminNotificationController' => $baseDir . '/api/v1/controllers/admin/system/AdminNotificationController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminSettingsController' => $baseDir . '/api/v1/controllers/admin/system/AdminSettingsController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminUpdateController' => $baseDir . '/api/v1/controllers/admin/system/AdminUpdateController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminUserController' => $baseDir . '/api/v1/controllers/admin/system/AdminUserController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminVersionController' => $baseDir . '/api/v1/controllers/admin/system/AdminVersionController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\Ticket\\AdminTicketAssignmentController' => $baseDir . '/api/v1/controllers/admin/ticket/AdminTicketAssignmentController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\Ticket\\AdminTicketController' => $baseDir . '/api/v1/controllers/admin/ticket/AdminTicketController.php',
    'TechCMS\\Api\\V1\\Controllers\\Admin\\Ticket\\AdminTicketDepartmentController' => $baseDir . '/api/v1/controllers/admin/ticket/AdminTicketDepartmentController.php',
    'TechCMS\\Api\\V1\\Controllers\\ApiBaseController' => $baseDir . '/api/v1/controllers/ApiBaseController.php',
    'TechCMS\\Api\\V1\\Controllers\\Client\\ClientAuthController' => $baseDir . '/api/v1/controllers/client/ClientAuthController.php',
    'TechCMS\\Api\\V1\\Controllers\\Client\\ClientBaseController' => $baseDir . '/api/v1/controllers/client/ClientBaseController.php',
    'TechCMS\\Api\\V1\\Controllers\\Client\\ClientDashboardController' => $baseDir . '/api/v1/controllers/client/ClientDashboardController.php',
    'TechCMS\\Api\\V1\\Controllers\\Client\\ClientDepartmentController' => $baseDir . '/api/v1/controllers/client/ClientDepartmentController.php',
    'TechCMS\\Api\\V1\\Controllers\\Client\\ClientInvoiceController' => $baseDir . '/api/v1/controllers/client/ClientInvoiceController.php',
    'TechCMS\\Api\\V1\\Controllers\\Client\\ClientLicenseController' => $baseDir . '/api/v1/controllers/client/ClientLicenseController.php',
    'TechCMS\\Api\\V1\\Controllers\\Client\\ClientLogController' => $baseDir . '/api/v1/controllers/client/ClientLogController.php',
    'TechCMS\\Api\\V1\\Controllers\\Client\\ClientProfileController' => $baseDir . '/api/v1/controllers/client/ClientProfileController.php',
    'TechCMS\\Api\\V1\\Controllers\\Client\\ClientServiceController' => $baseDir . '/api/v1/controllers/client/ClientServiceController.php',
    'TechCMS\\Api\\V1\\Controllers\\Client\\ClientTicketController' => $baseDir . '/api/v1/controllers/client/ClientTicketController.php',
    'TechCMS\\Api\\V1\\Controllers\\Client\\UpdatesController' => $baseDir . '/api/v1/controllers/Client/UpdatesController.php',
    'TechCMS\\Api\\V1\\Controllers\\LicenseApiController' => $baseDir . '/api/v1/controllers/LicenseApiController.php',
    'TechCMS\\Api\\V1\\Controllers\\Store\\StoreBaseController' => $baseDir . '/api/v1/controllers/store/StoreBaseController.php',
    'TechCMS\\Api\\V1\\Controllers\\TokenController' => $baseDir . '/api/v1/controllers/TokenController.php',
    'TechCMS\\Api\\V1\\Controllers\\UpdateApiController' => $baseDir . '/api/v1/controllers/UpdateApiController.php',
    'TechCMS\\Api\\V1\\Controllers\\Website\\ContactController' => $baseDir . '/api/v1/controllers/website/ContactController.php',
    'TechCMS\\Api\\V1\\Controllers\\Website\\TemplateController' => $baseDir . '/api/v1/controllers/website/TemplateController.php',
    'TechCMS\\Api\\V1\\Controllers\\Website\\WebsiteAuthController' => $baseDir . '/api/v1/controllers/website/WebsiteAuthController.php',
    'TechCMS\\Api\\V1\\Controllers\\Website\\WebsiteBaseController' => $baseDir . '/api/v1/controllers/website/WebsiteBaseController.php',
    'TechCMS\\Api\\V1\\Controllers\\Website\\WebsiteLogController' => $baseDir . '/api/v1/controllers/website/WebsiteLogController.php',
    'TechCMS\\Api\\V1\\Core\\JWT' => $baseDir . '/api/v1/core/JWT.php',
    'TechCMS\\Api\\V1\\Middleware\\Admin\\JWTAuthMiddleware' => $baseDir . '/api/v1/middleware/admin/JWTAuthMiddleware.php',
    'TechCMS\\Api\\V1\\Middleware\\ApiAuth' => $baseDir . '/api/v1/middleware/ApiAuth.php',
    'TechCMS\\Api\\V1\\Middleware\\ApiLogger' => $baseDir . '/api/v1/middleware/ApiLogger.php',
    'TechCMS\\Api\\V1\\Middleware\\Client\\ClientAuthMiddleware' => $baseDir . '/api/v1/middleware/client/ClientAuthMiddleware.php',
    'TechCMS\\Api\\V1\\Models\\ApiTokenModel' => $baseDir . '/api/v1/models/ApiTokenModel.php',
    'TechCMS\\Api\\V1\\Models\\Client' => $baseDir . '/api/v1/models/Client.php',
    'TechCMS\\Api\\V1\\Models\\Product' => $baseDir . '/api/v1/models/Product.php',
    'TechCMS\\Api\\V1\\Models\\ProductGroup' => $baseDir . '/api/v1/models/ProductGroup.php',
    'TechCMS\\Common\\Core\\App' => $baseDir . '/includes/common/core/App.php',
    'TechCMS\\Common\\Core\\Cron\\BaseCronTask' => $baseDir . '/includes/common/core/Cron/BaseCronTask.php',
    'TechCMS\\Common\\Core\\Database' => $baseDir . '/includes/common/core/Database.php',
    'TechCMS\\Common\\Core\\License\\LicenseManager' => $baseDir . '/includes/common/core/License/LicenseManager.php',
    'TechCMS\\Common\\Core\\Logger' => $baseDir . '/includes/common/core/Logger.php',
    'TechCMS\\Common\\Core\\ModuleInterface' => $baseDir . '/includes/common/core/ModuleInterface.php',
    'TechCMS\\Common\\Core\\ModuleManager' => $baseDir . '/includes/common/core/ModuleManager.php',
    'TechCMS\\Common\\Core\\NotificationManager' => $baseDir . '/includes/common/core/NotificationManager.php',
    'TechCMS\\Common\\Core\\RealTime' => $baseDir . '/includes/common/core/RealTime.php',
    'TechCMS\\Common\\Core\\Request' => $baseDir . '/includes/common/core/Request.php',
    'TechCMS\\Common\\Core\\Response' => $baseDir . '/includes/common/core/Response.php',
    'TechCMS\\Common\\Core\\Router' => $baseDir . '/includes/common/core/Router.php',
    'TechCMS\\Common\\Core\\ServerModuleInterface' => $baseDir . '/includes/common/core/ServerModuleInterface.php',
    'TechCMS\\Common\\Core\\Session' => $baseDir . '/includes/common/core/Session.php',
    'TechCMS\\Common\\Core\\StatsRealTime' => $baseDir . '/includes/common/core/StatsRealTime.php',
    'TechCMS\\Common\\Core\\ValidationException' => $baseDir . '/includes/common/core/ValidationException.php',
    'TechCMS\\Common\\Core\\Validator' => $baseDir . '/includes/common/core/Validator.php',
    'TechCMS\\Common\\Core\\View' => $baseDir . '/includes/common/core/View.php',
    'TechCMS\\Common\\Helpers\\EncryptionHelper' => $baseDir . '/includes/common/helpers/EncryptionHelper.php',
    'TechCMS\\Common\\Hooks\\HookManager' => $baseDir . '/includes/common/hooks/HookManager.php',
    'TechCMS\\Common\\Middleware\\AuthMiddleware' => $baseDir . '/includes/common/middleware/AuthMiddleware.php',
    'TechCMS\\Common\\Middleware\\SettingsMiddleware' => $baseDir . '/includes/common/middleware/SettingsMiddleware.php',
    'TechCMS\\Common\\Models\\BaseModel' => $baseDir . '/includes/common/models/BaseModel.php',
    'TechCMS\\Common\\Models\\ClientModel' => $baseDir . '/includes/common/models/ClientModel.php',
    'TechCMS\\Common\\Models\\CmsVersionModel' => $baseDir . '/includes/common/models/CmsVersionModel.php',
    'TechCMS\\Common\\Models\\InstallationUpdateModel' => $baseDir . '/includes/common/models/InstallationUpdateModel.php',
    'TechCMS\\Common\\Models\\InvoiceModel' => $baseDir . '/includes/common/models/InvoiceModel.php',
    'TechCMS\\Common\\Models\\LicenseModel' => $baseDir . '/includes/common/models/LicenseModel.php',
    'TechCMS\\Common\\Models\\LicenseTemplateModel' => $baseDir . '/includes/common/models/LicenseTemplateModel.php',
    'TechCMS\\Common\\Models\\NotificationModel' => $baseDir . '/includes/common/models/NotificationModel.php',
    'TechCMS\\Common\\Models\\PaymentModel' => $baseDir . '/includes/common/models/PaymentModel.php',
    'TechCMS\\Common\\Models\\SettingsModel' => $baseDir . '/includes/common/models/SettingsModel.php',
    'TechCMS\\Common\\Models\\TaxModel' => $baseDir . '/includes/common/models/TaxModel.php',
    'TechCMS\\Common\\Models\\TicketModel' => $baseDir . '/includes/common/models/TicketModel.php',
    'TechCMS\\Common\\Models\\TransactionModel' => $baseDir . '/includes/common/models/TransactionModel.php',
    'TechCMS\\Common\\Models\\UpdateDownloadModel' => $baseDir . '/includes/common/models/UpdateDownloadModel.php',
    'TechCMS\\Common\\Services\\ChunkedUploadService' => $baseDir . '/includes/common/services/ChunkedUploadService.php',
    'TechCMS\\Common\\Services\\UpdateDownloadService' => $baseDir . '/includes/common/services/UpdateDownloadService.php',
    'TechCMS\\Common\\Services\\UpdateFileService' => $baseDir . '/includes/common/services/UpdateFileService.php',
    'TechCMS\\Provisioning\\AutoProvisioning' => $baseDir . '/includes/provisioning/AutoProvisioning.php',
    'TechCMS\\Provisioning\\ProvisioningManager' => $baseDir . '/includes/provisioning/ProvisioningManager.php',
    'UnhandledMatchError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
    'ValueError' => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
);
