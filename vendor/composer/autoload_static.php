<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitf079824a9de88716d271e5ccd7837738
{
    public static $files = array (
        'ad155f8f1cf0d418fe49e248db8c661b' => __DIR__ . '/..' . '/react/promise/src/functions_include.php',
        '6e3fae29631ef280660b3cdad06f25a8' => __DIR__ . '/..' . '/symfony/deprecation-contracts/function.php',
        '0e6d7bf4a5811bfa5cf40c5ccd6fae6a' => __DIR__ . '/..' . '/symfony/polyfill-mbstring/bootstrap.php',
        '320cde22f66dd4f5d3fd621d3e88b98f' => __DIR__ . '/..' . '/symfony/polyfill-ctype/bootstrap.php',
        '8825ede83f2f289127722d4e842cf7e8' => __DIR__ . '/..' . '/symfony/polyfill-intl-grapheme/bootstrap.php',
        'e69f7f6ee287b969198c3c9d6777bd38' => __DIR__ . '/..' . '/symfony/polyfill-intl-normalizer/bootstrap.php',
        'b6b991a57620e2fb6b2f66f03fe9ddc2' => __DIR__ . '/..' . '/symfony/string/Resources/functions.php',
        'a4a119a56e50fbb293281d9a48007e0e' => __DIR__ . '/..' . '/symfony/polyfill-php80/bootstrap.php',
        '23c18046f52bef3eea034657bafda50f' => __DIR__ . '/..' . '/symfony/polyfill-php81/bootstrap.php',
        'decc78cc4436b1292c6c0d151b19445c' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/bootstrap.php',
        '9b38cf48e83f5d8f60375221cd213eee' => __DIR__ . '/..' . '/phpstan/phpstan/bootstrap.php',
    );

    public static $prefixLengthsPsr4 = array (
        'p' => 
        array (
            'phpseclib3\\' => 11,
        ),
        'T' => 
        array (
            'TechCMS\\Provisioning\\' => 21,
        ),
        'S' => 
        array (
            'Symfony\\Polyfill\\Php81\\' => 23,
            'Symfony\\Polyfill\\Php80\\' => 23,
            'Symfony\\Polyfill\\Mbstring\\' => 26,
            'Symfony\\Polyfill\\Intl\\Normalizer\\' => 33,
            'Symfony\\Polyfill\\Intl\\Grapheme\\' => 31,
            'Symfony\\Polyfill\\Ctype\\' => 23,
            'Symfony\\Contracts\\Service\\' => 26,
            'Symfony\\Contracts\\EventDispatcher\\' => 34,
            'Symfony\\Component\\String\\' => 25,
            'Symfony\\Component\\Stopwatch\\' => 28,
            'Symfony\\Component\\Process\\' => 26,
            'Symfony\\Component\\OptionsResolver\\' => 34,
            'Symfony\\Component\\Finder\\' => 25,
            'Symfony\\Component\\Filesystem\\' => 29,
            'Symfony\\Component\\EventDispatcher\\' => 34,
            'Symfony\\Component\\Console\\' => 26,
        ),
        'R' => 
        array (
            'React\\Stream\\' => 13,
            'React\\Socket\\' => 13,
            'React\\Promise\\' => 14,
            'React\\EventLoop\\' => 16,
            'React\\Dns\\' => 10,
            'React\\ChildProcess\\' => 19,
            'React\\Cache\\' => 12,
        ),
        'P' => 
        array (
            'Psr\\Log\\' => 8,
            'Psr\\EventDispatcher\\' => 20,
            'Psr\\Container\\' => 14,
            'PhpCsFixer\\' => 11,
            'ParagonIE\\ConstantTime\\' => 23,
            'PHPMailer\\PHPMailer\\' => 20,
        ),
        'M' => 
        array (
            'Monolog\\' => 8,
            'MessagePack\\' => 12,
        ),
        'F' => 
        array (
            'Fidry\\CpuCoreCounter\\' => 21,
        ),
        'E' => 
        array (
            'Evenement\\' => 10,
        ),
        'C' => 
        array (
            'Composer\\XdebugHandler\\' => 23,
            'Composer\\Semver\\' => 16,
            'Composer\\Pcre\\' => 14,
            'Clue\\React\\NDJson\\' => 18,
        ),
        'A' => 
        array (
            'Ably\\' => 5,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'phpseclib3\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib',
        ),
        'TechCMS\\Provisioning\\' => 
        array (
            0 => __DIR__ . '/../..' . '/includes/provisioning',
        ),
        'Symfony\\Polyfill\\Php81\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-php81',
        ),
        'Symfony\\Polyfill\\Php80\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-php80',
        ),
        'Symfony\\Polyfill\\Mbstring\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-mbstring',
        ),
        'Symfony\\Polyfill\\Intl\\Normalizer\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-intl-normalizer',
        ),
        'Symfony\\Polyfill\\Intl\\Grapheme\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-intl-grapheme',
        ),
        'Symfony\\Polyfill\\Ctype\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/polyfill-ctype',
        ),
        'Symfony\\Contracts\\Service\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/service-contracts',
        ),
        'Symfony\\Contracts\\EventDispatcher\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/event-dispatcher-contracts',
        ),
        'Symfony\\Component\\String\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/string',
        ),
        'Symfony\\Component\\Stopwatch\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/stopwatch',
        ),
        'Symfony\\Component\\Process\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/process',
        ),
        'Symfony\\Component\\OptionsResolver\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/options-resolver',
        ),
        'Symfony\\Component\\Finder\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/finder',
        ),
        'Symfony\\Component\\Filesystem\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/filesystem',
        ),
        'Symfony\\Component\\EventDispatcher\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/event-dispatcher',
        ),
        'Symfony\\Component\\Console\\' => 
        array (
            0 => __DIR__ . '/..' . '/symfony/console',
        ),
        'React\\Stream\\' => 
        array (
            0 => __DIR__ . '/..' . '/react/stream/src',
        ),
        'React\\Socket\\' => 
        array (
            0 => __DIR__ . '/..' . '/react/socket/src',
        ),
        'React\\Promise\\' => 
        array (
            0 => __DIR__ . '/..' . '/react/promise/src',
        ),
        'React\\EventLoop\\' => 
        array (
            0 => __DIR__ . '/..' . '/react/event-loop/src',
        ),
        'React\\Dns\\' => 
        array (
            0 => __DIR__ . '/..' . '/react/dns/src',
        ),
        'React\\ChildProcess\\' => 
        array (
            0 => __DIR__ . '/..' . '/react/child-process/src',
        ),
        'React\\Cache\\' => 
        array (
            0 => __DIR__ . '/..' . '/react/cache/src',
        ),
        'Psr\\Log\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/log/src',
        ),
        'Psr\\EventDispatcher\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/event-dispatcher/src',
        ),
        'Psr\\Container\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/container/src',
        ),
        'PhpCsFixer\\' => 
        array (
            0 => __DIR__ . '/..' . '/friendsofphp/php-cs-fixer/src',
        ),
        'ParagonIE\\ConstantTime\\' => 
        array (
            0 => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src',
        ),
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
        'Monolog\\' => 
        array (
            0 => __DIR__ . '/..' . '/monolog/monolog/src/Monolog',
        ),
        'MessagePack\\' => 
        array (
            0 => __DIR__ . '/..' . '/rybakit/msgpack/src',
        ),
        'Fidry\\CpuCoreCounter\\' => 
        array (
            0 => __DIR__ . '/..' . '/fidry/cpu-core-counter/src',
        ),
        'Evenement\\' => 
        array (
            0 => __DIR__ . '/..' . '/evenement/evenement/src',
        ),
        'Composer\\XdebugHandler\\' => 
        array (
            0 => __DIR__ . '/..' . '/composer/xdebug-handler/src',
        ),
        'Composer\\Semver\\' => 
        array (
            0 => __DIR__ . '/..' . '/composer/semver/src',
        ),
        'Composer\\Pcre\\' => 
        array (
            0 => __DIR__ . '/..' . '/composer/pcre/src',
        ),
        'Clue\\React\\NDJson\\' => 
        array (
            0 => __DIR__ . '/..' . '/clue/ndjson-react/src',
        ),
        'Ably\\' => 
        array (
            0 => __DIR__ . '/..' . '/ably/ably-php/src',
        ),
    );

    public static $classMap = array (
        'Attribute' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Attribute.php',
        'CURLStringFile' => __DIR__ . '/..' . '/symfony/polyfill-php81/Resources/stubs/CURLStringFile.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Normalizer' => __DIR__ . '/..' . '/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php',
        'PhpToken' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php',
        'ReturnTypeWillChange' => __DIR__ . '/..' . '/symfony/polyfill-php81/Resources/stubs/ReturnTypeWillChange.php',
        'SebastianBergmann\\Diff\\Chunk' => __DIR__ . '/..' . '/sebastian/diff/src/Chunk.php',
        'SebastianBergmann\\Diff\\ConfigurationException' => __DIR__ . '/..' . '/sebastian/diff/src/Exception/ConfigurationException.php',
        'SebastianBergmann\\Diff\\Diff' => __DIR__ . '/..' . '/sebastian/diff/src/Diff.php',
        'SebastianBergmann\\Diff\\Differ' => __DIR__ . '/..' . '/sebastian/diff/src/Differ.php',
        'SebastianBergmann\\Diff\\Exception' => __DIR__ . '/..' . '/sebastian/diff/src/Exception/Exception.php',
        'SebastianBergmann\\Diff\\InvalidArgumentException' => __DIR__ . '/..' . '/sebastian/diff/src/Exception/InvalidArgumentException.php',
        'SebastianBergmann\\Diff\\Line' => __DIR__ . '/..' . '/sebastian/diff/src/Line.php',
        'SebastianBergmann\\Diff\\LongestCommonSubsequenceCalculator' => __DIR__ . '/..' . '/sebastian/diff/src/LongestCommonSubsequenceCalculator.php',
        'SebastianBergmann\\Diff\\MemoryEfficientLongestCommonSubsequenceCalculator' => __DIR__ . '/..' . '/sebastian/diff/src/MemoryEfficientLongestCommonSubsequenceCalculator.php',
        'SebastianBergmann\\Diff\\Output\\AbstractChunkOutputBuilder' => __DIR__ . '/..' . '/sebastian/diff/src/Output/AbstractChunkOutputBuilder.php',
        'SebastianBergmann\\Diff\\Output\\DiffOnlyOutputBuilder' => __DIR__ . '/..' . '/sebastian/diff/src/Output/DiffOnlyOutputBuilder.php',
        'SebastianBergmann\\Diff\\Output\\DiffOutputBuilderInterface' => __DIR__ . '/..' . '/sebastian/diff/src/Output/DiffOutputBuilderInterface.php',
        'SebastianBergmann\\Diff\\Output\\StrictUnifiedDiffOutputBuilder' => __DIR__ . '/..' . '/sebastian/diff/src/Output/StrictUnifiedDiffOutputBuilder.php',
        'SebastianBergmann\\Diff\\Output\\UnifiedDiffOutputBuilder' => __DIR__ . '/..' . '/sebastian/diff/src/Output/UnifiedDiffOutputBuilder.php',
        'SebastianBergmann\\Diff\\Parser' => __DIR__ . '/..' . '/sebastian/diff/src/Parser.php',
        'SebastianBergmann\\Diff\\TimeEfficientLongestCommonSubsequenceCalculator' => __DIR__ . '/..' . '/sebastian/diff/src/TimeEfficientLongestCommonSubsequenceCalculator.php',
        'Stringable' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/Stringable.php',
        'TechCMS\\Admin\\Models\\AdminModel' => __DIR__ . '/../..' . '/includes/admin/models/AdminModel.php',
        'TechCMS\\Admin\\Models\\AdminProductDraft' => __DIR__ . '/../..' . '/includes/admin/models/AdminProductDraft.php',
        'TechCMS\\Admin\\Models\\AdminServerModel' => __DIR__ . '/../..' . '/includes/admin/models/AdminServerModel.php',
        'TechCMS\\Admin\\Models\\AdminServiceConfigurationModel' => __DIR__ . '/../..' . '/includes/admin/models/AdminServiceConfigurationModel.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\AdminAuthController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/AdminAuthController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\AdminBaseController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/AdminBaseController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\Client\\AdminClientController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/client/AdminClientController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\Invoice\\AdminInvoiceController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/invoice/AdminInvoiceController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\LicenseTemplateController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/license/AdminLicenseTemplateController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\License\\AdminLicenseController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/license/AdminLicenseController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\License\\AdminLicenseUpdateController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/license/AdminLicenseUpdateController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\Module\\AdminModuleController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/module/AdminModuleController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\Payment\\AdminPaymentController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/payment/AdminPaymentController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminAutomationController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/system/AdminAutomationController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminDashboardController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/system/AdminDashboardController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminLogController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/system/AdminLogController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminNotificationController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/system/AdminNotificationController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminSettingsController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/system/AdminSettingsController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminUpdateController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/system/AdminUpdateController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminUserController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/system/AdminUserController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\System\\AdminVersionController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/system/AdminVersionController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\Ticket\\AdminTicketAssignmentController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/ticket/AdminTicketAssignmentController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\Ticket\\AdminTicketController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/ticket/AdminTicketController.php',
        'TechCMS\\Api\\V1\\Controllers\\Admin\\Ticket\\AdminTicketDepartmentController' => __DIR__ . '/../..' . '/api/v1/controllers/admin/ticket/AdminTicketDepartmentController.php',
        'TechCMS\\Api\\V1\\Controllers\\ApiBaseController' => __DIR__ . '/../..' . '/api/v1/controllers/ApiBaseController.php',
        'TechCMS\\Api\\V1\\Controllers\\Client\\ClientAuthController' => __DIR__ . '/../..' . '/api/v1/controllers/client/ClientAuthController.php',
        'TechCMS\\Api\\V1\\Controllers\\Client\\ClientBaseController' => __DIR__ . '/../..' . '/api/v1/controllers/client/ClientBaseController.php',
        'TechCMS\\Api\\V1\\Controllers\\Client\\ClientDashboardController' => __DIR__ . '/../..' . '/api/v1/controllers/client/ClientDashboardController.php',
        'TechCMS\\Api\\V1\\Controllers\\Client\\ClientDepartmentController' => __DIR__ . '/../..' . '/api/v1/controllers/client/ClientDepartmentController.php',
        'TechCMS\\Api\\V1\\Controllers\\Client\\ClientInvoiceController' => __DIR__ . '/../..' . '/api/v1/controllers/client/ClientInvoiceController.php',
        'TechCMS\\Api\\V1\\Controllers\\Client\\ClientLicenseController' => __DIR__ . '/../..' . '/api/v1/controllers/client/ClientLicenseController.php',
        'TechCMS\\Api\\V1\\Controllers\\Client\\ClientLogController' => __DIR__ . '/../..' . '/api/v1/controllers/client/ClientLogController.php',
        'TechCMS\\Api\\V1\\Controllers\\Client\\ClientProfileController' => __DIR__ . '/../..' . '/api/v1/controllers/client/ClientProfileController.php',
        'TechCMS\\Api\\V1\\Controllers\\Client\\ClientServiceController' => __DIR__ . '/../..' . '/api/v1/controllers/client/ClientServiceController.php',
        'TechCMS\\Api\\V1\\Controllers\\Client\\ClientTicketController' => __DIR__ . '/../..' . '/api/v1/controllers/client/ClientTicketController.php',
        'TechCMS\\Api\\V1\\Controllers\\Client\\UpdatesController' => __DIR__ . '/../..' . '/api/v1/controllers/client/ClientUpdatesController.php',
        'TechCMS\\Api\\V1\\Controllers\\LicenseApiController' => __DIR__ . '/../..' . '/api/v1/controllers/LicenseApiController.php',
        'TechCMS\\Api\\V1\\Controllers\\Store\\StoreBaseController' => __DIR__ . '/../..' . '/api/v1/controllers/store/StoreBaseController.php',
        'TechCMS\\Api\\V1\\Controllers\\TokenController' => __DIR__ . '/../..' . '/api/v1/controllers/TokenController.php',
        'TechCMS\\Api\\V1\\Controllers\\UpdateApiController' => __DIR__ . '/../..' . '/api/v1/controllers/UpdateApiController.php',
        'TechCMS\\Api\\V1\\Controllers\\Website\\WebsiteBaseController' => __DIR__ . '/../..' . '/api/v1/controllers/website/WebsiteBaseController.php',
        'TechCMS\\Api\\V1\\Controllers\\website\\WebsiteAuthController' => __DIR__ . '/../..' . '/api/v1/controllers/website/WebsiteAuthController.php',
        'TechCMS\\Api\\V1\\Controllers\\website\\WebsiteContactController' => __DIR__ . '/../..' . '/api/v1/controllers/website/WebsiteContactController.php',
        'TechCMS\\Api\\V1\\Controllers\\website\\WebsiteLogController' => __DIR__ . '/../..' . '/api/v1/controllers/website/WebsiteLogController.php',
        'TechCMS\\Api\\V1\\Controllers\\website\\WebsiteTemplateController' => __DIR__ . '/../..' . '/api/v1/controllers/website/WebsiteTemplateController.php',
        'TechCMS\\Api\\V1\\Core\\JWT' => __DIR__ . '/../..' . '/api/v1/core/JWT.php',
        'TechCMS\\Api\\V1\\Middleware\\Admin\\JWTAuthMiddleware' => __DIR__ . '/../..' . '/api/v1/middleware/admin/JWTAuthMiddleware.php',
        'TechCMS\\Api\\V1\\Middleware\\ApiAuth' => __DIR__ . '/../..' . '/api/v1/middleware/ApiAuth.php',
        'TechCMS\\Api\\V1\\Middleware\\ApiLogger' => __DIR__ . '/../..' . '/api/v1/middleware/ApiLogger.php',
        'TechCMS\\Api\\V1\\Middleware\\Client\\ClientAuthMiddleware' => __DIR__ . '/../..' . '/api/v1/middleware/client/ClientAuthMiddleware.php',
        'TechCMS\\Api\\V1\\Models\\ApiTokenModel' => __DIR__ . '/../..' . '/api/v1/models/ApiTokenModel.php',
        'TechCMS\\Api\\V1\\Models\\Client' => __DIR__ . '/../..' . '/api/v1/models/Client.php',
        'TechCMS\\Api\\V1\\Models\\Product' => __DIR__ . '/../..' . '/api/v1/models/Product.php',
        'TechCMS\\Api\\V1\\Models\\ProductGroup' => __DIR__ . '/../..' . '/api/v1/models/ProductGroup.php',
        'TechCMS\\Common\\Core\\App' => __DIR__ . '/../..' . '/includes/common/core/App.php',
        'TechCMS\\Common\\Core\\Cron\\BaseCronTask' => __DIR__ . '/../..' . '/includes/common/core/Cron/BaseCronTask.php',
        'TechCMS\\Common\\Core\\Database' => __DIR__ . '/../..' . '/includes/common/core/Database.php',
        'TechCMS\\Common\\Core\\License\\LicenseManager' => __DIR__ . '/../..' . '/includes/common/core/License/LicenseManager.php',
        'TechCMS\\Common\\Core\\Logger' => __DIR__ . '/../..' . '/includes/common/core/Logger.php',
        'TechCMS\\Common\\Core\\ModuleInterface' => __DIR__ . '/../..' . '/includes/common/core/ModuleInterface.php',
        'TechCMS\\Common\\Core\\ModuleManager' => __DIR__ . '/../..' . '/includes/common/core/ModuleManager.php',
        'TechCMS\\Common\\Core\\NotificationManager' => __DIR__ . '/../..' . '/includes/common/core/NotificationManager.php',
        'TechCMS\\Common\\Core\\RealTime' => __DIR__ . '/../..' . '/includes/common/core/RealTime.php',
        'TechCMS\\Common\\Core\\Request' => __DIR__ . '/../..' . '/includes/common/core/Request.php',
        'TechCMS\\Common\\Core\\Response' => __DIR__ . '/../..' . '/includes/common/core/Response.php',
        'TechCMS\\Common\\Core\\Router' => __DIR__ . '/../..' . '/includes/common/core/Router.php',
        'TechCMS\\Common\\Core\\ServerModuleInterface' => __DIR__ . '/../..' . '/includes/common/core/ServerModuleInterface.php',
        'TechCMS\\Common\\Core\\Session' => __DIR__ . '/../..' . '/includes/common/core/Session.php',
        'TechCMS\\Common\\Core\\StatsRealTime' => __DIR__ . '/../..' . '/includes/common/core/StatsRealTime.php',
        'TechCMS\\Common\\Core\\ValidationException' => __DIR__ . '/../..' . '/includes/common/core/ValidationException.php',
        'TechCMS\\Common\\Core\\Validator' => __DIR__ . '/../..' . '/includes/common/core/Validator.php',
        'TechCMS\\Common\\Core\\View' => __DIR__ . '/../..' . '/includes/common/core/View.php',
        'TechCMS\\Common\\Helpers\\EncryptionHelper' => __DIR__ . '/../..' . '/includes/common/helpers/EncryptionHelper.php',
        'TechCMS\\Common\\Hooks\\HookManager' => __DIR__ . '/../..' . '/includes/common/hooks/HookManager.php',
        'TechCMS\\Common\\Middleware\\AuthMiddleware' => __DIR__ . '/../..' . '/includes/common/middleware/AuthMiddleware.php',
        'TechCMS\\Common\\Middleware\\SettingsMiddleware' => __DIR__ . '/../..' . '/includes/common/middleware/SettingsMiddleware.php',
        'TechCMS\\Common\\Models\\BaseModel' => __DIR__ . '/../..' . '/includes/common/models/BaseModel.php',
        'TechCMS\\Common\\Models\\ClientModel' => __DIR__ . '/../..' . '/includes/common/models/ClientModel.php',
        'TechCMS\\Common\\Models\\CmsVersionModel' => __DIR__ . '/../..' . '/includes/common/models/CmsVersionModel.php',
        'TechCMS\\Common\\Models\\InstallationUpdateModel' => __DIR__ . '/../..' . '/includes/common/models/InstallationUpdateModel.php',
        'TechCMS\\Common\\Models\\InvoiceModel' => __DIR__ . '/../..' . '/includes/common/models/InvoiceModel.php',
        'TechCMS\\Common\\Models\\LicenseModel' => __DIR__ . '/../..' . '/includes/common/models/LicenseModel.php',
        'TechCMS\\Common\\Models\\LicenseTemplateModel' => __DIR__ . '/../..' . '/includes/common/models/LicenseTemplateModel.php',
        'TechCMS\\Common\\Models\\NotificationModel' => __DIR__ . '/../..' . '/includes/common/models/NotificationModel.php',
        'TechCMS\\Common\\Models\\PaymentModel' => __DIR__ . '/../..' . '/includes/common/models/PaymentModel.php',
        'TechCMS\\Common\\Models\\SettingsModel' => __DIR__ . '/../..' . '/includes/common/models/SettingsModel.php',
        'TechCMS\\Common\\Models\\TaxModel' => __DIR__ . '/../..' . '/includes/common/models/TaxModel.php',
        'TechCMS\\Common\\Models\\TicketModel' => __DIR__ . '/../..' . '/includes/common/models/TicketModel.php',
        'TechCMS\\Common\\Models\\TransactionModel' => __DIR__ . '/../..' . '/includes/common/models/TransactionModel.php',
        'TechCMS\\Common\\Models\\UpdateDownloadModel' => __DIR__ . '/../..' . '/includes/common/models/UpdateDownloadModel.php',
        'TechCMS\\Common\\Services\\ChunkedUploadService' => __DIR__ . '/../..' . '/includes/common/services/ChunkedUploadService.php',
        'TechCMS\\Common\\Services\\UpdateDownloadService' => __DIR__ . '/../..' . '/includes/common/services/UpdateDownloadService.php',
        'TechCMS\\Common\\Services\\UpdateFileService' => __DIR__ . '/../..' . '/includes/common/services/UpdateFileService.php',
        'TechCMS\\Provisioning\\AutoProvisioning' => __DIR__ . '/../..' . '/includes/provisioning/AutoProvisioning.php',
        'TechCMS\\Provisioning\\ProvisioningManager' => __DIR__ . '/../..' . '/includes/provisioning/ProvisioningManager.php',
        'UnhandledMatchError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php',
        'ValueError' => __DIR__ . '/..' . '/symfony/polyfill-php80/Resources/stubs/ValueError.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitf079824a9de88716d271e5ccd7837738::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitf079824a9de88716d271e5ccd7837738::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitf079824a9de88716d271e5ccd7837738::$classMap;

        }, null, ClassLoader::class);
    }
}
