import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import logger from '@/services/logger'
import type { LicenseTemplate } from '@/types/template'

// Types pour le panier
export interface CartItem {
  id: string
  template: LicenseTemplate
  quantity: number
  customizations: Record<string, any>
  subtotal: number
}

export interface Cart {
  items: CartItem[]
  total: number
  tax_amount: number
  grand_total: number
  currency: string
}

export const useCartStore = defineStore('cart', () => {
  // État
  const items = ref<CartItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Configuration
  const TAX_RATE = 0.20 // 20% TVA
  const CURRENCY = 'EUR'

  // Getters
  const itemCount = computed(() => 
    items.value.reduce((total, item) => total + item.quantity, 0)
  )

  const subtotal = computed(() =>
    items.value.reduce((total, item) => total + item.subtotal, 0)
  )

  const taxAmount = computed(() => subtotal.value * TAX_RATE)

  const total = computed(() => subtotal.value + taxAmount.value)

  const cart = computed((): Cart => ({
    items: items.value,
    total: subtotal.value,
    tax_amount: taxAmount.value,
    grand_total: total.value,
    currency: CURRENCY
  }))

  const isEmpty = computed(() => items.value.length === 0)

  const hasItem = computed(() => (templateId: number) => {
    return items.value.some(item => item.template.id === templateId)
  })

  const getItem = computed(() => (templateId: number) => {
    return items.value.find(item => item.template.id === templateId)
  })

  // Utilitaires
  const generateCartItemId = () => {
    return `cart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  const calculateSubtotal = (template: LicenseTemplate, quantity: number, customizations: Record<string, any> = {}) => {
    let basePrice = parseFloat(template.price?.toString() || '0')

    logger.info('[CartStore] Calcul subtotal', {
      template_id: template.id,
      template_name: template.name,
      original_price: template.price,
      parsed_price: basePrice,
      quantity,
      customizations
    })

    // Validation du prix de base
    if (isNaN(basePrice) || basePrice < 0) {
      logger.warn('[CartStore] Prix invalide détecté', {
        template_id: template.id,
        price: template.price
      })
      basePrice = 0
    }

    // Ajouter les frais de setup si c'est la première fois
    if (template.setup_fee && template.setup_fee > 0) {
      const setupFee = parseFloat(template.setup_fee.toString())
      if (!isNaN(setupFee)) {
        basePrice += setupFee
      }
    }

    // Appliquer les customizations (domaines/installations supplémentaires)
    if (customizations.extra_domains) {
      basePrice += customizations.extra_domains * 10 // 10€ par domaine supplémentaire
    }

    if (customizations.extra_installations) {
      basePrice += customizations.extra_installations * 5 // 5€ par installation supplémentaire
    }

    const subtotal = basePrice * quantity

    logger.info('[CartStore] Subtotal calculé', {
      template_id: template.id,
      base_price: basePrice,
      quantity,
      subtotal
    })

    return subtotal
  }

  const saveToLocalStorage = () => {
    try {
      localStorage.setItem('techcms_cart', JSON.stringify(items.value))
      logger.debug('[CartStore] Panier sauvegardé dans localStorage')
    } catch (error) {
      logger.error('[CartStore] Erreur sauvegarde localStorage', { error })
    }
  }

  const loadFromLocalStorage = () => {
    try {
      const saved = localStorage.getItem('techcms_cart')
      if (saved) {
        items.value = JSON.parse(saved)

        // Recalculer tous les subtotals au cas où la logique aurait changé
        items.value.forEach(item => {
          const newSubtotal = calculateSubtotal(item.template, item.quantity, item.customizations)
          if (item.subtotal !== newSubtotal) {
            logger.info('[CartStore] Recalcul subtotal pour item existant', {
              item_id: item.id,
              old_subtotal: item.subtotal,
              new_subtotal: newSubtotal
            })
            item.subtotal = newSubtotal
          }
        })

        logger.info('[CartStore] Panier chargé depuis localStorage', {
          items: items.value.length
        })

        // Sauvegarder les subtotals recalculés
        saveToLocalStorage()
      }
    } catch (error) {
      logger.error('[CartStore] Erreur chargement localStorage', { error })
      items.value = []
    }
  }

  // Actions
  const addItem = (template: LicenseTemplate, quantity = 1, customizations = {}) => {
    try {
      const existingItemIndex = items.value.findIndex(item => item.template.id === template.id)
      
      if (existingItemIndex !== -1) {
        // Mettre à jour la quantité si l'item existe déjà
        items.value[existingItemIndex].quantity += quantity
        items.value[existingItemIndex].subtotal = calculateSubtotal(
          template,
          items.value[existingItemIndex].quantity,
          customizations
        )
      } else {
        // Ajouter un nouvel item
        const cartItem: CartItem = {
          id: generateCartItemId(),
          template,
          quantity,
          customizations,
          subtotal: calculateSubtotal(template, quantity, customizations)
        }
        items.value.push(cartItem)
      }

      // Sauvegarder dans le localStorage
      saveToLocalStorage()
      
      logger.info('[CartStore] Item ajouté au panier', {
        template_id: template.id,
        template_name: template.name,
        quantity,
        total_items: itemCount.value
      })
      
      return true
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de l\'ajout au panier'
      logger.error('[CartStore] Erreur addItem', { error: err.message })
      return false
    }
  }

  const updateQuantity = (itemId: string, quantity: number) => {
    try {
      const itemIndex = items.value.findIndex(item => item.id === itemId)
      
      if (itemIndex !== -1) {
        if (quantity <= 0) {
          removeItem(itemId)
        } else {
          items.value[itemIndex].quantity = quantity
          items.value[itemIndex].subtotal = calculateSubtotal(
            items.value[itemIndex].template,
            quantity,
            items.value[itemIndex].customizations
          )
          saveToLocalStorage()
          
          logger.info('[CartStore] Quantité mise à jour', {
            item_id: itemId,
            new_quantity: quantity
          })
        }
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la mise à jour'
      logger.error('[CartStore] Erreur updateQuantity', { error: err.message })
    }
  }

  const removeItem = (itemId: string) => {
    try {
      const itemIndex = items.value.findIndex(item => item.id === itemId)
      
      if (itemIndex !== -1) {
        const removedItem = items.value.splice(itemIndex, 1)[0]
        saveToLocalStorage()
        
        logger.info('[CartStore] Item supprimé du panier', {
          item_id: itemId,
          template_name: removedItem.template.name
        })
      }
    } catch (err: any) {
      error.value = err.message || 'Erreur lors de la suppression'
      logger.error('[CartStore] Erreur removeItem', { error: err.message })
    }
  }

  const clearCart = () => {
    try {
      items.value = []
      saveToLocalStorage()
      
      logger.info('[CartStore] Panier vidé')
    } catch (err: any) {
      error.value = err.message || 'Erreur lors du vidage du panier'
      logger.error('[CartStore] Erreur clearCart', { error: err.message })
    }
  }

  const clearError = () => {
    error.value = null
  }

  // Initialisation
  const initialize = () => {
    loadFromLocalStorage()
    logger.info('[CartStore] Store panier initialisé')
  }

  return {
    // État
    items,
    loading,
    error,
    
    // Getters
    itemCount,
    subtotal,
    taxAmount,
    total,
    cart,
    isEmpty,
    hasItem,
    getItem,
    
    // Actions
    addItem,
    updateQuantity,
    removeItem,
    clearCart,
    clearError,
    initialize
  }
}, {
  persist: {
    key: 'techcms-cart',
    storage: localStorage,
    paths: ['items']
  }
})
