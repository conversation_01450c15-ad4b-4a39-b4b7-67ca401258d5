<template>
  <div class="store-layout">
    <!-- En-tête boutique -->
    <header class="store-header">
      <div class="header-container">
        <!-- Logo et navigation -->
        <div class="header-left">
          <router-link to="/" class="store-logo">
            <i class="fas fa-shopping-bag"></i>
            <span>TechCMS Store</span>
          </router-link>
          
          <nav class="main-nav">
            <router-link to="/" class="nav-link">
              <i class="fas fa-home"></i>
              Produits
            </router-link>
            <router-link to="/cart" class="nav-link">
              <i class="fas fa-shopping-cart"></i>
              Panier
              <span v-if="cartStore.itemCount > 0" class="cart-badge">
                {{ cartStore.itemCount }}
              </span>
            </router-link>
          </nav>
        </div>

        <!-- Actions utilisateur -->
        <div class="header-right">
          <div class="cart-summary" v-if="cartStore.itemCount > 0">
            <span class="cart-total">{{ formatPrice(cartStore.total) }}</span>
          </div>
          
          <div class="user-menu">
            <router-link to="/login" class="btn btn-outline btn-sm">
              <i class="fas fa-sign-in-alt"></i>
              Connexion
            </router-link>
            <router-link to="/register" class="btn btn-primary btn-sm">
              <i class="fas fa-user-plus"></i>
              Inscription
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- Contenu principal -->
    <main class="store-content">
      <slot />
    </main>

    <!-- Panier flottant -->
    <div v-if="cartStore.itemCount > 0" class="floating-cart">
      <router-link to="/cart" class="floating-cart-btn">
        <i class="fas fa-shopping-cart"></i>
        <span class="cart-count">{{ cartStore.itemCount }}</span>
        <span class="cart-total">{{ formatPrice(cartStore.total) }}</span>
      </router-link>
    </div>

    <!-- Pied de page -->
    <footer class="store-footer">
      <div class="footer-container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>TechCMS Store</h3>
            <p>Votre boutique de licences TechCMS</p>
          </div>
          
          <div class="footer-section">
            <h4>Liens utiles</h4>
            <ul>
              <li><a href="/website">Site vitrine</a></li>
              <li><a href="/client">Espace client</a></li>
              <li><a href="/admin">Administration</a></li>
            </ul>
          </div>
          
          <div class="footer-section">
            <h4>Support</h4>
            <ul>
              <li><a href="/website/contact">Contact</a></li>
              <li><a href="/website/about">À propos</a></li>
            </ul>
          </div>
        </div>
        
        <div class="footer-bottom">
          <p>&copy; 2024 TechCMS. Tous droits réservés.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { useCartStore } from '@/stores/cart'

// Store
const cartStore = useCartStore()

// Méthodes utilitaires
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price)
}
</script>

<style scoped>
.store-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

/* En-tête */
.store-header {
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.store-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: var(--primary-color);
  font-size: 1.25rem;
  font-weight: 700;
}

.store-logo i {
  font-size: 1.5rem;
}

.main-nav {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  text-decoration: none;
  color: var(--text-secondary);
  border-radius: 6px;
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: var(--primary-color);
  background: var(--hover-bg);
}

.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--danger-color);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.cart-summary {
  font-weight: 600;
  color: var(--primary-color);
}

.user-menu {
  display: flex;
  gap: 0.75rem;
}

/* Contenu principal */
.store-content {
  flex: 1;
  min-height: calc(100vh - 70px - 200px); /* Header + Footer approximatif */
}

/* Panier flottant */
.floating-cart {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 50;
}

.floating-cart-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: var(--primary-color);
  color: white;
  text-decoration: none;
  border-radius: 50px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
  font-weight: 600;
}

.floating-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.cart-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.875rem;
}

/* Pied de page */
.store-footer {
  background: var(--card-bg);
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 3rem 2rem 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.footer-section p {
  color: var(--text-secondary);
  margin: 0;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-section a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
  color: var(--text-secondary);
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  cursor: pointer;
  font-size: 0.875rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #1e40af;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--border-color);
}

.btn-outline:hover {
  background: var(--hover-bg);
  border-color: var(--primary-color);
}

/* Responsive */
@media (max-width: 768px) {
  .header-container {
    padding: 0 1rem;
    height: 60px;
  }
  
  .header-left {
    gap: 1rem;
  }
  
  .main-nav {
    gap: 0.75rem;
  }
  
  .nav-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .header-right {
    gap: 1rem;
  }
  
  .user-menu {
    gap: 0.5rem;
  }
  
  .floating-cart {
    bottom: 1rem;
    right: 1rem;
  }
  
  .floating-cart-btn {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
  
  .footer-container {
    padding: 2rem 1rem 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .store-logo span {
    display: none;
  }
  
  .main-nav .nav-link span {
    display: none;
  }
  
  .cart-summary {
    display: none;
  }
  
  .floating-cart-btn .cart-total {
    display: none;
  }
}
</style>
