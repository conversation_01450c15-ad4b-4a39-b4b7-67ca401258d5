export declare const eachWeekOfIntervalWithOptions: import("./types.js").FPFn2<
  import("../eachWeekOfInterval.js").EachWeekOfIntervalResult<
    import("../fp.js").Interval<
      import("../fp.js").DateArg<Date>,
      import("../fp.js").DateArg<Date>
    >,
    | import("../eachWeekOfInterval.js").EachWeekOfIntervalOptions<Date>
    | undefined
  >,
  | import("../eachWeekOfInterval.js").EachWeekOfIntervalOptions<Date>
  | undefined,
  import("../fp.js").Interval<
    import("../fp.js").DateArg<Date>,
    import("../fp.js").DateArg<Date>
  >
>;
